/* Modern Video Upload Hub Styles */

.App {
  text-align: left;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #000000;
}

::-webkit-scrollbar-thumb {
  background: #374151;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Platform-specific colors and gradients */
.lulustream {
  border-color: #8b5cf6;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(139, 92, 246, 0.05));
}

.streamp2p {
  border-color: #3b82f6;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
}

.rpmshare {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
}

.filemoon {
  border-color: #f59e0b;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
}

.upnshare {
  border-color: #ec4899;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(236, 72, 153, 0.05));
}

/* Enhanced progress bar colors */
.progress-lulustream {
  background: linear-gradient(90deg, #8b5cf6, #a855f7, #c084fc);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.progress-streamp2p {
  background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.progress-rpmshare {
  background: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.progress-filemoon {
  background: linear-gradient(90deg, #f59e0b, #fbbf24, #fcd34d);
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.progress-upnshare {
  background: linear-gradient(90deg, #ec4899, #f472b6, #f9a8d4);
  box-shadow: 0 0 10px rgba(236, 72, 153, 0.5);
}

/* Status colors with better contrast */
.status-queued {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.status-uploading {
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.status-processing {
  color: #a855f7;
  background: rgba(168, 85, 247, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.status-completed {
  color: #34d399;
  background: rgba(52, 211, 153, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.status-failed, .status-error {
  color: #f87171;
  background: rgba(248, 113, 113, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

/* Enhanced animations */
.pulse-glow {
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 8px currentColor;
  }
  50% {
    opacity: 0.6;
    box-shadow: 0 0 16px currentColor, 0 0 24px currentColor;
  }
}

.fade-in {
  animation: fade-in 0.6s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.upload-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Button hover effects */
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Focus styles for accessibility */
button:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading spinner enhancement */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}