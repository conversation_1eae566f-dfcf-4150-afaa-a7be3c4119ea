# Set default behavior to automatically normalize line endings
* text=auto

# Force batch scripts to always use CRLF line endings so that if a repo is accessed
# in Windows via a file share from Linux, the scripts will work.
*.{cmd,[cC][mM][dD]} text eol=crlf
*.{bat,[bB][aA][tT]} text eol=crlf

# Force bash scripts to always use LF line endings so that if a repo is accessed
# in Unix via a file share from Windows, the scripts will work.
*.sh text eol=lf

# Web files should be normalized and converted to native line endings on checkout
*.html text
*.css text
*.js text
*.jsx text
*.ts text
*.tsx text
*.json text
*.md text
*.txt text
*.xml text
*.yml text
*.yaml text

# Python files
*.py text

# Configuration files
*.config.js text
*.config.json text
.env* text
.gitignore text
.gitattributes text

# Package files
package.json text
package-lock.json text
yarn.lock text
requirements.txt text

# Images should be treated as binary
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text

# Audio files
*.mp3 binary
*.wav binary
*.ogg binary

# Video files
*.mp4 binary
*.avi binary
*.mkv binary
*.mov binary
*.wmv binary
*.flv binary
*.webm binary

# Archives
*.zip binary
*.tar binary
*.gz binary
*.7z binary
*.rar binary

# Executables
*.exe binary
*.dll binary
*.so binary
*.dylib binary

# Node modules should be ignored but if present, treat as binary
node_modules/** binary

# Python cache
__pycache__/** binary
*.pyc binary
*.pyo binary
*.pyd binary

# Build outputs
build/** binary
dist/** binary
*.min.js binary
*.min.css binary
