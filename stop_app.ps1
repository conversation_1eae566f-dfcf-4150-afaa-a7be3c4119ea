# Video Upload Automation Hub Stopper (PowerShell)
Write-Host "========================================" -ForegroundColor Red
Write-Host "  Video Upload Automation Hub Stopper" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host ""

Write-Host "Stopping all related processes..." -ForegroundColor Yellow
Write-Host ""

$stoppedProcesses = 0

# Stop processes on port 8001 (Backend)
Write-Host "Checking for Backend processes on port 8001..." -ForegroundColor Cyan
try {
    $backendConnections = Get-NetTCPConnection -LocalPort 8001 -ErrorAction SilentlyContinue
    if ($backendConnections) {
        $backendConnections | ForEach-Object {
            $processId = $_.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "✓ Killing backend process: $($process.ProcessName) (PID: $processId)" -ForegroundColor Green
                Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
                $stoppedProcesses++
            }
        }
    } else {
        Write-Host "No backend processes found on port 8001" -ForegroundColor Gray
    }
} catch {
    Write-Host "Error checking backend processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Stop processes on port 3000 (Frontend)
Write-Host "Checking for Frontend processes on port 3000..." -ForegroundColor Cyan
try {
    $frontendConnections = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
    if ($frontendConnections) {
        $frontendConnections | ForEach-Object {
            $processId = $_.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "✓ Killing frontend process: $($process.ProcessName) (PID: $processId)" -ForegroundColor Green
                Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
                $stoppedProcesses++
            }
        }
    } else {
        Write-Host "No frontend processes found on port 3000" -ForegroundColor Gray
    }
} catch {
    Write-Host "Error checking frontend processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Kill any remaining Python processes running uvicorn
Write-Host "Checking for Python uvicorn processes..." -ForegroundColor Cyan
try {
    $pythonProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue
    $uvicornProcesses = $pythonProcesses | Where-Object {
        try {
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($_.Id)").CommandLine
            return $commandLine -like "*uvicorn*server:app*"
        } catch {
            return $false
        }
    }
    
    if ($uvicornProcesses) {
        $uvicornProcesses | ForEach-Object {
            Write-Host "✓ Killing Python uvicorn process (PID: $($_.Id))" -ForegroundColor Green
            Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
            $stoppedProcesses++
        }
    } else {
        Write-Host "No Python uvicorn processes found" -ForegroundColor Gray
    }
} catch {
    Write-Host "Error checking Python processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Kill any remaining Node processes running React dev server
Write-Host "Checking for Node React processes..." -ForegroundColor Cyan
try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    $reactProcesses = $nodeProcesses | Where-Object {
        try {
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($_.Id)").CommandLine
            return $commandLine -like "*react-scripts*start*"
        } catch {
            return $false
        }
    }
    
    if ($reactProcesses) {
        $reactProcesses | ForEach-Object {
            Write-Host "✓ Killing Node React process (PID: $($_.Id))" -ForegroundColor Green
            Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
            $stoppedProcesses++
        }
    } else {
        Write-Host "No Node React processes found" -ForegroundColor Gray
    }
} catch {
    Write-Host "Error checking Node processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Cleanup completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Total processes stopped: $stoppedProcesses" -ForegroundColor Cyan
Write-Host ""
Write-Host "You can now safely:" -ForegroundColor Yellow
Write-Host "- Run .\start_app.ps1 to restart" -ForegroundColor Yellow
Write-Host "- Make code changes" -ForegroundColor Yellow
Write-Host "- Close this window" -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to exit"
