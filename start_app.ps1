# Video Upload Automation Hub Starter (PowerShell)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Video Upload Automation Hub Starter" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ and try again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Node.js is installed
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✓ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js and try again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Stopping any existing servers..." -ForegroundColor Yellow

# Kill processes on port 8001 (Backend)
$backendProcesses = Get-NetTCPConnection -LocalPort 8001 -ErrorAction SilentlyContinue
if ($backendProcesses) {
    Write-Host "Stopping backend server on port 8001..." -ForegroundColor Yellow
    $backendProcesses | ForEach-Object {
        Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue
    }
}

# Kill processes on port 3000 (Frontend)
$frontendProcesses = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
if ($frontendProcesses) {
    Write-Host "Stopping frontend server on port 3000..." -ForegroundColor Yellow
    $frontendProcesses | ForEach-Object {
        Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue
    }
}

Write-Host ""
Write-Host "Starting Backend Server..." -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Check if backend directory exists
$backendPath = Join-Path $PSScriptRoot "backend"
if (-not (Test-Path $backendPath)) {
    Write-Host "✗ ERROR: backend directory not found" -ForegroundColor Red
    Write-Host "Current directory: $PSScriptRoot" -ForegroundColor Yellow
    Write-Host "Please make sure you're running this from the project root" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if server.py exists
$serverPath = Join-Path $backendPath "server.py"
if (-not (Test-Path $serverPath)) {
    Write-Host "✗ ERROR: server.py not found in backend directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Start backend server
Write-Host "Starting backend server..." -ForegroundColor Cyan
$backendJob = Start-Process -FilePath "python" -ArgumentList "-m", "uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8001", "--reload" -WorkingDirectory $backendPath -WindowStyle Normal -PassThru

# Wait a moment for backend to start
Start-Sleep -Seconds 3

Write-Host ""
Write-Host "Starting Frontend Development Server..." -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Check if frontend directory exists
$frontendPath = Join-Path $PSScriptRoot "frontend"
if (-not (Test-Path $frontendPath)) {
    Write-Host "✗ ERROR: frontend directory not found" -ForegroundColor Red
    Write-Host "Please make sure the frontend folder exists" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Start frontend server
Write-Host "Starting frontend server..." -ForegroundColor Cyan
$env:DANGEROUSLY_DISABLE_HOST_CHECK = "true"
$frontendJob = Start-Process -FilePath "npm" -ArgumentList "start" -WorkingDirectory $frontendPath -WindowStyle Normal -PassThru

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Both servers are starting up..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Backend:  http://localhost:8001" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to open the application in your browser..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Open the application in default browser
Start-Process "http://localhost:3000"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Application is now running!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Backend Process ID: $($backendJob.Id)" -ForegroundColor Cyan
Write-Host "Frontend Process ID: $($frontendJob.Id)" -ForegroundColor Cyan
Write-Host ""
Write-Host "To stop the servers, run: .\stop_app.ps1" -ForegroundColor Yellow
Write-Host "Or close the terminal windows manually" -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to exit this script (servers will continue running)"
