@echo off
echo ========================================
echo   Video Upload Automation Hub Stopper
echo ========================================
echo.

echo Stopping all related processes...
echo.

:: Kill processes by port numbers
echo Checking for Backend processes on port 8001...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do (
    echo Killing backend process PID: %%a
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo ✓ Backend process stopped
    )
)

echo.
echo Checking for Frontend processes on port 3000...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    echo Killing frontend process PID: %%a
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo ✓ Frontend process stopped
    )
)

echo.
echo Killing any remaining Python/Node processes...

:: Kill Python processes that might be running uvicorn
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq python.exe" /fo table /nh 2^>nul') do (
    for /f "tokens=*" %%b in ('wmic process where "ProcessId=%%a" get CommandLine /value 2^>nul ^| findstr "uvicorn"') do (
        echo Killing Python uvicorn process PID: %%a
        taskkill /f /pid %%a >nul 2>&1
    )
)

:: Kill Node processes that might be running React dev server
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq node.exe" /fo table /nh 2^>nul') do (
    for /f "tokens=*" %%b in ('wmic process where "ProcessId=%%a" get CommandLine /value 2^>nul ^| findstr "react-scripts"') do (
        echo Killing Node React process PID: %%a
        taskkill /f /pid %%a >nul 2>&1
    )
)

:: Close any command windows with our server titles
echo.
echo Closing server terminal windows...
taskkill /f /fi "WindowTitle eq Backend Server*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Frontend Server*" >nul 2>&1

echo.
echo ========================================
echo   All processes have been stopped!
echo ========================================
echo.
echo You can now safely:
echo - Run start_app.bat to restart
echo - Make code changes
echo - Close this window
echo.
pause
