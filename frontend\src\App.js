import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Upload,
  Download,
  Trash2,
  Check<PERSON>ircle,
  XCircle,
  Clock,
  AlertTriangle,
  Loader2,
  Plus,
  Monitor,
  Zap,
  BarChart3
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';
import './App.css';

// Get backend URL from environment
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';

// Platform configurations
const PLATFORMS = [
  { name: 'lulustream', display: 'Lulustream', color: 'purple', icon: '🟣' },
  { name: 'streamp2p', display: 'StreamP2P', color: 'blue', icon: '🔵' },
  { name: 'rpmshare', display: 'RPMShare', color: 'green', icon: '🟢' },
  { name: 'filemoon', display: 'FileMoon', color: 'yellow', icon: '🟡' },
  { name: 'upnshare', display: 'UpnShare', color: 'pink', icon: '🩷' }
];

function App() {
  // State management
  const [urls, setUrls] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [queueStatus, setQueueStatus] = useState({
    total_queued: 0,
    total_in_progress: 0,
    total_completed: 0,
    total_failed: 0,
    current_queue: [],
    active_uploads: [],
    completed_uploads: []
  });
  const [sessionId, setSessionId] = useState(null);
  const [platformStatus, setPlatformStatus] = useState({});
  
  // WebSocket connection
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  
  // Audio notification system
  const playCompletionSound = useCallback(() => {
    try {
      const audio = new Audio('/audio/completion.mp3');
      audio.volume = 0.3;
      audio.play().catch(e => console.log('Could not play completion sound:', e));
    } catch (e) {
      console.log('Completion audio not available');
    }
  }, []);
  
  const playErrorSound = useCallback(() => {
    try {
      const audio = new Audio('/audio/error.mp3');
      audio.volume = 0.3;
      audio.play().catch(e => console.log('Could not play error sound:', e));
    } catch (e) {
      console.log('Error audio not available');
    }
  }, []);
  
  // WebSocket connection management
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }
    
    const wsUrl = BACKEND_URL.replace('http', 'ws') + '/ws';
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('WebSocket connected');
      wsRef.current = ws;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'initial_state') {
          setQueueStatus({
            total_queued: data.data.queue.length,
            total_in_progress: data.data.active.length,
            total_completed: data.data.completed.length,
            total_failed: data.data.failed.length,
            current_queue: data.data.queue,
            active_uploads: data.data.active,
            completed_uploads: data.data.completed
          });
        } else if (data.type === 'task_update') {
          // Handle real-time task updates
          const task = data.data;
          
          // Check if task just completed all platforms
          const completedPlatforms = Object.values(task.platform_status || {}).filter(status => status === 'completed').length;
          const totalPlatforms = PLATFORMS.length;
          
          if (completedPlatforms === totalPlatforms && task.status === 'completed') {
            playCompletionSound();
            toast.success(`✅ Upload completed: ${task.filename}`, {
              duration: 4000,
              style: {
                background: '#065f46',
                color: '#ffffff',
                border: '1px solid #10b981'
              }
            });
          } else if (task.status === 'failed') {
            playErrorSound();
            toast.error(`❌ Upload failed: ${task.filename}`, {
              duration: 4000,
              style: {
                background: '#7f1d1d',
                color: '#ffffff',
                border: '1px solid #ef4444'
              }
            });
          }
          
          // Refresh queue status
          fetchQueueStatus();
        }
      } catch (e) {
        console.error('Error parsing WebSocket message:', e);
      }
    };
    
    ws.onclose = () => {
      console.log('WebSocket disconnected');
      wsRef.current = null;
      
      // Reconnect after 3 seconds
      reconnectTimeoutRef.current = setTimeout(() => {
        connectWebSocket();
      }, 3000);
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

  }, [playCompletionSound, playErrorSound, fetchQueueStatus]);
  
  // Fetch queue status
  const fetchQueueStatus = useCallback(async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/queue/status`);
      if (response.ok) {
        const data = await response.json();
        setQueueStatus(data);
      }
    } catch (error) {
      console.error('Error fetching queue status:', error);
    }
  }, []);
  
  // Fetch platform status
  const fetchPlatformStatus = useCallback(async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/platforms/status`);
      if (response.ok) {
        const data = await response.json();
        setPlatformStatus(data);
      }
    } catch (error) {
      console.error('Error fetching platform status:', error);
    }
  }, []);
  
  // Initialize on component mount
  useEffect(() => {
    connectWebSocket();
    fetchQueueStatus();
    fetchPlatformStatus();
    
    // Set up periodic refresh
    const interval = setInterval(() => {
      fetchQueueStatus();
    }, 10000); // Refresh every 10 seconds
    
    return () => {
      clearInterval(interval);
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [connectWebSocket, fetchQueueStatus, fetchPlatformStatus]);
  
  // Handle URL submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!urls.trim()) {
      toast.error('Please enter at least one URL');
      return;
    }
    
    const urlList = urls.split('\n').filter(url => url.trim()).map(url => url.trim());
    
    if (urlList.length === 0) {
      toast.error('Please enter valid URLs');
      return;
    }
    
    if (urlList.length > 50) {
      toast.error('Maximum 50 URLs allowed at once');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`${BACKEND_URL}/api/upload/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: urlList,
          session_id: sessionId
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setSessionId(data.session_id);
        setUrls(''); // Clear input
        
        toast.success(`✅ Queued ${urlList.length} URLs for upload`, {
          duration: 3000,
          style: {
            background: '#065f46',
            color: '#ffffff',
            border: '1px solid #10b981'
          }
        });
        
        // Refresh status
        fetchQueueStatus();
      } else {
        const error = await response.json();
        toast.error(`❌ Error: ${error.detail}`);
      }
    } catch (error) {
      toast.error(`❌ Network error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle CSV download
  const handleDownloadCSV = async () => {
    try {
      const url = sessionId 
        ? `${BACKEND_URL}/api/download/csv?session_id=${sessionId}`
        : `${BACKEND_URL}/api/download/csv`;
        
      const response = await fetch(url);
      
      if (response.ok) {
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = `embed_codes_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(downloadUrl);
        
        toast.success('✅ CSV file downloaded successfully');
      } else {
        const error = await response.json();
        toast.error(`❌ Download failed: ${error.detail}`);
      }
    } catch (error) {
      toast.error(`❌ Download error: ${error.message}`);
    }
  };
  
  // Cancel upload
  const handleCancelUpload = async (taskId) => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/upload/cancel/${taskId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        toast.success('✅ Upload cancelled');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to cancel upload');
      }
    } catch (error) {
      toast.error(`❌ Cancel error: ${error.message}`);
    }
  };
  
  // Clear completed uploads
  const handleClearCompleted = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/uploads/clear-completed`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        toast.success('✅ Completed uploads cleared');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to clear completed uploads');
      }
    } catch (error) {
      toast.error(`❌ Clear error: ${error.message}`);
    }
  };
  
  // Clear entire queue
  const handleClearQueue = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/queue/clear`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        toast.success('✅ Entire queue cleared');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to clear queue');
      }
    } catch (error) {
      toast.error(`❌ Clear queue error: ${error.message}`);
    }
  };
  
  // End all tasks
  const handleEndAllTasks = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/uploads/end-all`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const result = await response.json();
        toast.success(`✅ ${result.message}`);
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to end all tasks');
      }
    } catch (error) {
      toast.error(`❌ End tasks error: ${error.message}`);
    }
  };
  
  // Format time remaining
  const formatTimeRemaining = (seconds) => {
    if (!seconds || seconds <= 0) return '0s';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };
  
  return (
    <div className="min-h-screen bg-black text-white">
      <Toaster position="top-right" />

      {/* Modern Header */}
      <header className="bg-gradient-to-r from-gray-900 via-black to-gray-900 border-b border-gray-700 sticky top-0 z-50 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                <Upload className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Video Upload Hub
                </h1>
                <p className="text-gray-400 text-sm">Multi-platform automation</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 bg-gray-800 px-3 py-1.5 rounded-full">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-sm font-medium">Online</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 py-6 space-y-8">
        {/* Platform Status Grid */}
        <section>
          <div className="flex items-center space-x-3 mb-6">
            <Monitor className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-semibold">Platform Status</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {PLATFORMS.map((platform) => (
              <div key={platform.name} className="bg-gray-900 border border-gray-700 rounded-xl p-4 hover:border-gray-600 transition-all duration-200">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-2xl">{platform.icon}</span>
                  {platformStatus[platform.name]?.configured ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-400" />
                  )}
                </div>
                <h3 className="font-medium text-white mb-1">{platform.display}</h3>
                <p className={`text-sm ${platformStatus[platform.name]?.configured ? 'text-green-400' : 'text-red-400'}`}>
                  {platformStatus[platform.name]?.configured ? 'Ready' : 'Not configured'}
                </p>
              </div>
            ))}
          </div>
        </section>
        
        {/* URL Input Section */}
        <section>
          <div className="flex items-center space-x-3 mb-6">
            <Plus className="w-6 h-6 text-green-400" />
            <h2 className="text-xl font-semibold">Add Remote URLs</h2>
          </div>

          <div className="bg-gray-900 border border-gray-700 rounded-xl p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Remote URLs (one per line, maximum 50)
                </label>
                <textarea
                  value={urls}
                  onChange={(e) => setUrls(e.target.value)}
                  placeholder="https://example.com/video1.mp4&#10;https://example.com/video2.mp4&#10;https://example.com/video3.mp4"
                  className="w-full h-40 px-4 py-3 bg-black border border-gray-600 rounded-lg text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                  disabled={isSubmitting}
                />
                <div className="flex justify-between items-center mt-3 text-sm">
                  <span className="text-gray-400">
                    {urls.split('\n').filter(url => url.trim()).length} URLs entered
                  </span>
                  <div className="flex space-x-4 text-gray-400">
                    <span>Queued: <span className="text-yellow-400 font-medium">{queueStatus.total_queued}</span></span>
                    <span>Active: <span className="text-blue-400 font-medium">{queueStatus.total_in_progress}</span></span>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                <button
                  type="submit"
                  disabled={isSubmitting || !urls.trim()}
                  className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200 shadow-lg"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Queueing...
                    </>
                  ) : (
                    <>
                      <Upload className="w-5 h-5 mr-2" />
                      Queue for Upload
                    </>
                  )}
                </button>

                <button
                  type="button"
                  onClick={handleDownloadCSV}
                  disabled={queueStatus.total_completed === 0}
                  className="flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200 shadow-lg"
                >
                  <Download className="w-5 h-5 mr-2" />
                  Download CSV ({queueStatus.total_completed})
                </button>

                <button
                  type="button"
                  onClick={handleClearCompleted}
                  disabled={queueStatus.total_completed === 0}
                  className="flex items-center px-4 py-3 bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200"
                >
                  <Trash2 className="w-5 h-5 mr-2" />
                  Clear Completed
                </button>

                <button
                  type="button"
                  onClick={handleClearQueue}
                  disabled={queueStatus.total_queued === 0 && queueStatus.total_in_progress === 0}
                  className="flex items-center px-4 py-3 bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-500 hover:to-orange-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200"
                >
                  <Trash2 className="w-5 h-5 mr-2" />
                  Clear Queue
                </button>

                <button
                  type="button"
                  onClick={handleEndAllTasks}
                  disabled={queueStatus.total_in_progress === 0 && queueStatus.total_queued === 0}
                  className="flex items-center px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200"
                >
                  <XCircle className="w-5 h-5 mr-2" />
                  End All Tasks
                </button>
              </div>

              <div className="flex justify-between items-center pt-4 border-t border-gray-700 text-sm text-gray-400">
                <span>Total Completed: <span className="text-green-400 font-medium">{queueStatus.total_completed}</span></span>
                <span>Failed: <span className="text-red-400 font-medium">{queueStatus.total_failed}</span></span>
              </div>
            </form>
          </div>
        </section>
        
        {/* Queue Statistics */}
        <section>
          <div className="flex items-center space-x-3 mb-6">
            <BarChart3 className="w-6 h-6 text-yellow-400" />
            <h2 className="text-xl font-semibold">Queue Statistics</h2>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gradient-to-br from-yellow-900/20 to-yellow-800/20 border border-yellow-700/30 rounded-xl p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-400 text-sm font-medium">Queued</p>
                  <p className="text-3xl font-bold text-yellow-300">{queueStatus.total_queued}</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
            </div>

            <div className="bg-gradient-to-br from-blue-900/20 to-blue-800/20 border border-blue-700/30 rounded-xl p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-400 text-sm font-medium">In Progress</p>
                  <p className="text-3xl font-bold text-blue-300">{queueStatus.total_in_progress}</p>
                </div>
                <Zap className="w-8 h-8 text-blue-400" />
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-900/20 to-green-800/20 border border-green-700/30 rounded-xl p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-400 text-sm font-medium">Completed</p>
                  <p className="text-3xl font-bold text-green-300">{queueStatus.total_completed}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
            </div>

            <div className="bg-gradient-to-br from-red-900/20 to-red-800/20 border border-red-700/30 rounded-xl p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-400 text-sm font-medium">Failed</p>
                  <p className="text-3xl font-bold text-red-300">{queueStatus.total_failed}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-400" />
              </div>
            </div>
          </div>
        </section>
        
        {/* Active Uploads */}
        {queueStatus.active_uploads.length > 0 && (
          <section>
            <div className="flex items-center space-x-3 mb-6">
              <Zap className="w-6 h-6 text-blue-400" />
              <h2 className="text-xl font-semibold">Active Uploads</h2>
              <span className="bg-blue-600 text-white text-sm px-2 py-1 rounded-full">
                {queueStatus.active_uploads.length}
              </span>
            </div>

            <div className="space-y-6">
              {queueStatus.active_uploads.map((task) => (
                <div key={task.id} className="bg-gray-900 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-all duration-200">
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-white mb-2 truncate">{task.filename}</h3>
                      <p className="text-sm text-gray-400 truncate">{task.url}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-sm text-blue-400 capitalize bg-blue-900/30 px-2 py-1 rounded">
                          {task.status.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleCancelUpload(task.id)}
                      className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors"
                      title="Cancel Upload"
                    >
                      <XCircle className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Platform Progress Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {PLATFORMS.map((platform) => {
                      const progress = task.progress?.[platform.name] || 0;
                      const speed = task.speeds?.[platform.name] || 0;
                      const eta = task.eta_seconds?.[platform.name] || 0;
                      const status = task.platform_status?.[platform.name] || 'queued';
                      const hasEmbedCode = task.embed_codes?.[platform.name];

                      return (
                        <div key={platform.name} className="bg-black border border-gray-600 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-xl">{platform.icon}</span>
                              <span className="font-medium text-white">{platform.display}</span>
                            </div>
                            {hasEmbedCode && (
                              <CheckCircle className="w-5 h-5 text-green-400" />
                            )}
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className={`status-${status.replace('_', '-')} capitalize`}>
                                {status.replace('_', ' ')}
                              </span>
                              <span className="text-white font-medium">{progress.toFixed(1)}%</span>
                            </div>

                            <div className="w-full bg-gray-700 rounded-full h-2">
                              <div
                                className={`h-full transition-all duration-300 rounded-full progress-${platform.name}`}
                                style={{ width: `${Math.max(progress, 0)}%` }}
                              />
                            </div>

                            <div className="flex justify-between text-xs text-gray-400">
                              {speed > 0 && (
                                <span>{speed.toFixed(1)} MB/s</span>
                              )}
                              {eta > 0 && (
                                <span>ETA: {formatTimeRemaining(eta)}</span>
                              )}
                              {task.error_messages?.[platform.name] && (
                                <span className="text-red-400 flex items-center">
                                  <AlertTriangle className="w-3 h-3 mr-1" />
                                  Error
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Queue Section */}
        {queueStatus.current_queue.length > 0 && (
          <section>
            <div className="flex items-center space-x-3 mb-6">
              <Clock className="w-6 h-6 text-yellow-400" />
              <h2 className="text-xl font-semibold">Upload Queue</h2>
              <span className="bg-yellow-600 text-white text-sm px-2 py-1 rounded-full">
                {queueStatus.current_queue.length}
              </span>
            </div>

            <div className="bg-gray-900 border border-gray-700 rounded-xl p-6">
              <div className="space-y-3">
                {queueStatus.current_queue.slice(0, 10).map((task, index) => (
                  <div key={task.id} className="flex items-center justify-between p-4 bg-black border border-gray-600 rounded-lg">
                    <div className="flex items-center space-x-4 flex-1 min-w-0">
                      <span className="text-sm text-yellow-400 font-medium bg-yellow-900/30 px-2 py-1 rounded">
                        #{index + 1}
                      </span>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-white truncate">{task.filename}</p>
                        <p className="text-sm text-gray-400 truncate">{task.url}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-yellow-400 bg-yellow-900/30 px-2 py-1 rounded">Waiting</span>
                      <Clock className="w-4 h-4 text-yellow-400" />
                    </div>
                  </div>
                ))}
                {queueStatus.current_queue.length > 10 && (
                  <div className="text-center py-4 text-gray-400 text-sm border-t border-gray-700">
                    ... and {queueStatus.current_queue.length - 10} more items in queue
                  </div>
                )}
              </div>
            </div>
          </section>
        )}

        {/* Completed Uploads */}
        {queueStatus.completed_uploads.length > 0 && (
          <section>
            <div className="flex items-center space-x-3 mb-6">
              <CheckCircle className="w-6 h-6 text-green-400" />
              <h2 className="text-xl font-semibold">Recent Completed</h2>
              <span className="bg-green-600 text-white text-sm px-2 py-1 rounded-full">
                {queueStatus.completed_uploads.length}
              </span>
            </div>

            <div className="space-y-4">
              {queueStatus.completed_uploads.slice(-5).reverse().map((task) => (
                <div key={task.id} className="bg-gray-900 border border-green-700/50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-white flex items-center space-x-2 mb-2">
                        <CheckCircle className="w-5 h-5 text-green-400" />
                        <span className="truncate">{task.filename}</span>
                      </h3>
                      <p className="text-sm text-gray-400 truncate">{task.url}</p>
                    </div>
                    <div className="text-sm text-green-400 bg-green-900/30 px-3 py-1 rounded">
                      {task.completed_at && new Date(task.completed_at).toLocaleTimeString()}
                    </div>
                  </div>

                  {/* Platform Status Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                    {PLATFORMS.map((platform) => {
                      const hasEmbedCode = task.embed_codes?.[platform.name];

                      return (
                        <div key={platform.name} className="bg-black border border-gray-600 rounded-lg p-3 text-center">
                          <div className="flex items-center justify-center space-x-2 mb-2">
                            <span className="text-lg">{platform.icon}</span>
                            {hasEmbedCode ? (
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-400" />
                            )}
                          </div>
                          <div className="text-sm font-medium text-white">{platform.display}</div>
                          <div className={`text-xs mt-1 ${hasEmbedCode ? 'text-green-400' : 'text-red-400'}`}>
                            {hasEmbedCode ? 'Success' : 'Failed'}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-700 mt-12">
        <div className="max-w-6xl mx-auto px-4 py-8 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Upload className="w-5 h-5 text-blue-400" />
            <span className="text-lg font-semibold text-white">Video Upload Automation Hub</span>
          </div>
          <p className="text-gray-400 text-sm">
            Multi-platform video upload automation for Lulustream, StreamP2P, RPMShare, FileMoon, and UpnShare
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;