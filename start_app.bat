@echo off
echo ========================================
echo   Video Upload Automation Hub Starter
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js and try again
    pause
    exit /b 1
)

:: Kill any existing processes on ports 8001 and 3000
echo Stopping any existing servers...
netstat -ano | findstr :8001 | findstr LISTENING >nul && (
    echo Stopping backend server on port 8001...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8001 ^| findstr LISTENING') do taskkill /f /pid %%a >nul 2>&1
)

netstat -ano | findstr :3000 | findstr LISTENING >nul && (
    echo Stopping frontend server on port 3000...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do taskkill /f /pid %%a >nul 2>&1
)

echo.
echo Starting Backend Server...
echo ========================

:: Check if backend directory and server.py exist
if not exist "%~dp0backend" (
    echo ERROR: backend directory not found
    echo Current directory: %~dp0
    echo Please make sure you're running this from the project root
    pause
    exit /b 1
)

if not exist "%~dp0backend\server.py" (
    echo ERROR: server.py not found in backend directory
    pause
    exit /b 1
)

:: Create backend startup script
echo @echo off > "%~dp0start_backend.bat"
echo cd /d "%~dp0backend" >> "%~dp0start_backend.bat"
echo python -m uvicorn server:app --host 0.0.0.0 --port 8001 --reload >> "%~dp0start_backend.bat"
echo pause >> "%~dp0start_backend.bat"

:: Start backend in a new window
start "Backend Server" "%~dp0start_backend.bat"

:: Wait a moment for backend to start
timeout /t 3 /nobreak >nul

echo.
echo Starting Frontend Development Server...
echo =====================================

:: Check if frontend directory exists
if not exist "%~dp0frontend" (
    echo ERROR: frontend directory not found
    echo Please make sure the frontend folder exists
    pause
    exit /b 1
)

:: Create frontend startup script
echo @echo off > "%~dp0start_frontend.bat"
echo cd /d "%~dp0frontend" >> "%~dp0start_frontend.bat"
echo set DANGEROUSLY_DISABLE_HOST_CHECK=true >> "%~dp0start_frontend.bat"
echo npm start >> "%~dp0start_frontend.bat"
echo pause >> "%~dp0start_frontend.bat"

:: Start frontend in a new window
start "Frontend Server" "%~dp0start_frontend.bat"

echo.
echo ========================================
echo   Both servers are starting up...
echo ========================================
echo.
echo Backend:  http://localhost:8001
echo Frontend: http://localhost:3000
echo.
echo Press any key to open the application in your browser...
pause >nul

:: Open the application in default browser
start http://localhost:3000

echo.
echo ========================================
echo   Application is now running!
echo ========================================
echo.
echo To stop the servers, run: stop_app.bat
echo Or close the terminal windows manually
echo.
pause
