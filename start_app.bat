@echo off
echo ========================================
echo   Video Upload Automation Hub Starter
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js and try again
    pause
    exit /b 1
)

:: Kill any existing processes on ports 8001 and 3000
echo Checking for existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do (
    echo Killing existing backend process on port 8001...
    taskkill /f /pid %%a >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    echo Killing existing frontend process on port 3000...
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo Starting Backend Server...
echo ========================

:: Start backend in a new window
start "Backend Server" cmd /k "cd /d "%~dp0" && python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload"

:: Wait a moment for backend to start
timeout /t 3 /nobreak >nul

echo.
echo Starting Frontend Development Server...
echo =====================================

:: Start frontend in a new window
start "Frontend Server" cmd /k "cd /d "%~dp0frontend" && set DANGEROUSLY_DISABLE_HOST_CHECK=true && set HOST=0.0.0.0 && npm start"

echo.
echo ========================================
echo   Both servers are starting up...
echo ========================================
echo.
echo Backend:  http://localhost:8001
echo Frontend: http://localhost:3000
echo.
echo Press any key to open the application in your browser...
pause >nul

:: Open the application in default browser
start http://localhost:3000

echo.
echo ========================================
echo   Application is now running!
echo ========================================
echo.
echo To stop the servers, run: stop_app.bat
echo Or close the terminal windows manually
echo.
pause
